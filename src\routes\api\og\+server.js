import { html } from 'satori-html';
import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';

export async function GET({ url }) {
	const type = url.searchParams.get('type') || 'static';

	if (type === 'static') {
		return await generateStaticOG();
	}

	// Default fallback
	return await generateStaticOG();
}

async function loadFont() {
	const fontResponse = await fetch('http://pixeldrain.com/api/file/52yBhNXR');
	const fontBuffer = await fontResponse.arrayBuffer();
	return fontBuffer;
}

async function generateStaticOG() {
	const markup = html`
		<div style="
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 1200px;
			height: 630px;
			background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e1b4b 100%);
			padding: 60px;
		">
				<!-- Title -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
				">
					<h1 style="
						font-family: 'Inter';
						font-size: 80px;
						font-weight: 800;
						color: #f8fafc;
						margin: 0 0 16px 0;
						text-align: center;
						line-height: 1.1;
						letter-spacing: -0.02em;
					">anithing.moe</h1>
				</div>

				<!-- Subtitle -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 48px;
				">
					<p style="
						font-family: 'Inter';
						font-size: 24px;
						color: #cbd5e1;
						margin: 0;
						text-align: center;
						font-weight: 400;
						line-height: 1.4;
						max-width: 900px;
					">Your ultimate gateway to the world of Japanese media. Search, track, and manage all your lists in one unified experience.</p>
				</div>

				<!-- Section heading -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
				">
					<h2 style="
						font-family: 'Inter';
						font-size: 32px;
						color: #38bdf8;
						margin: 0;
						text-align: center;
						font-weight: 600;
					">What Anithing.moe Offers</h2>
				</div>

				<!-- Features summary -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-bottom: 32px;
				">
					<p style="
						font-family: 'Inter';
						font-size: 22px;
						color: #7dd3fc;
						margin: 0;
						text-align: center;
						font-weight: 600;
					">Unified Search • Centralized Lists • Track Friends Aniwhere</p>
				</div>

				<!-- Description -->
				<div style="
					display: flex;
					flex-direction: column;
					align-items: center;
				">
					<p style="
						font-family: 'Inter';
						font-size: 18px;
						color: #cbd5e1;
						margin: 0;
						text-align: center;
						line-height: 1.4;
						max-width: 800px;
					">Find anime, manga, VNs, and LNs across MAL, AniList, Kitsu, VNDB and more. Manage all your lists and track friends across all platforms seamlessly.</p>
				</div>
			</div>
		`;

	const fontData = await loadFont();

	const svg = await satori(markup, {
		width: 1200,
		height: 630,
		fonts: [
			{
				name: 'Inter',
				data: fontData,
				weight: 400,
				style: 'normal',
			},
		]
	});

	const resvg = new Resvg(svg);
	const pngData = resvg.render();
	const pngBuffer = pngData.asPng();

	return new Response(pngBuffer, {
		headers: {
			'Content-Type': 'image/png',
			'Cache-Control': 'public, max-age=31536000, immutable'
		}
	});
}