import { html } from 'satori-html';
import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';

export async function GET({ url }) {
	const type = url.searchParams.get('type') || 'static';

	if (type === 'static') {
		return await generateStaticOG();
	}

	// Default fallback
	return await generateStaticOG();
}

async function loadFont() {
	const fontResponse = await fetch('http://pixeldrain.com/api/file/52yBhNXR');
	const fontBuffer = await fontResponse.arrayBuffer();
	return fontBuffer;
}

async function generateStaticOG() {
	const markup = html`
		<div style="
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 1200px;
			height: 630px;
			background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e1b4b 100%);
			padding: 60px;
			position: relative;
		">
			<div style="
				position: absolute;
				top: 80px;
				left: 100px;
				width: 120px;
				height: 120px;
				background: radial-gradient(circle, rgba(56, 189, 248, 0.3) 0%, rgba(56, 189, 248, 0.1) 70%, transparent 100%);
				border-radius: 50%;
				filter: blur(2px);
				display: flex;
			"></div>

			<div style="
				position: absolute;
				bottom: 100px;
				right: 120px;
				width: 160px;
				height: 160px;
				background: radial-gradient(circle, rgba(125, 211, 252, 0.25) 0%, rgba(125, 211, 252, 0.08) 70%, transparent 100%);
				border-radius: 50%;
				filter: blur(3px);
				display: flex;
			"></div>

			<div style="
				position: absolute;
				top: 150px;
				right: 200px;
				width: 80px;
				height: 80px;
				background: radial-gradient(circle, rgba(168, 85, 247, 0.2) 0%, rgba(168, 85, 247, 0.05) 70%, transparent 100%);
				border-radius: 50%;
				filter: blur(1px);
				display: flex;
			"></div>

			<div style="
				display: flex;
				flex-direction: column;
				align-items: center;
				z-index: 10;
			">
				<h1 style="
					font-family: 'Inter';
					font-size: 96px;
					font-weight: 900;
					color: #f8fafc;
					margin: 0 0 24px 0;
					text-align: center;
					line-height: 1;
					letter-spacing: -0.03em;
					text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
				">anithing.moe</h1>

				<p style="
					font-family: 'Inter';
					font-size: 32px;
					color: #38bdf8;
					margin: 0 0 40px 0;
					text-align: center;
					font-weight: 600;
					letter-spacing: -0.01em;
				">Unified Japanese Media Hub</p>

				<div style="
					display: flex;
					gap: 40px;
					align-items: center;
					justify-content: center;
				">
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						text-align: center;
					">
						<div style="
							width: 60px;
							height: 60px;
							background: linear-gradient(135deg, #38bdf8, #0ea5e9);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 12px;
							box-shadow: 0 8px 25px rgba(56, 189, 248, 0.3);
						">
							<span style="
								font-size: 24px;
								color: white;
								font-weight: bold;
							">🔍</span>
						</div>
						<span style="
							font-family: 'Inter';
							font-size: 16px;
							color: #7dd3fc;
							font-weight: 600;
						">Search</span>
					</div>

					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						text-align: center;
					">
						<div style="
							width: 60px;
							height: 60px;
							background: linear-gradient(135deg, #7dd3fc, #38bdf8);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 12px;
							box-shadow: 0 8px 25px rgba(125, 211, 252, 0.3);
						">
							<span style="
								font-size: 24px;
								color: white;
								font-weight: bold;
							">📚</span>
						</div>
						<span style="
							font-family: 'Inter';
							font-size: 16px;
							color: #7dd3fc;
							font-weight: 600;
						">Lists</span>
					</div>

					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						text-align: center;
					">
						<div style="
							width: 60px;
							height: 60px;
							background: linear-gradient(135deg, #a855f7, #8b5cf6);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 12px;
							box-shadow: 0 8px 25px rgba(168, 85, 247, 0.3);
						">
							<span style="
								font-size: 24px;
								color: white;
								font-weight: bold;
							">👥</span>
						</div>
						<span style="
							font-family: 'Inter';
							font-size: 16px;
							color: #7dd3fc;
							font-weight: 600;
						">Friends</span>
					</div>
				</div>
			</div>
		</div>
		`;

	const fontData = await loadFont();

	const svg = await satori(markup, {
		width: 1200,
		height: 630,
		fonts: [
			{
				name: 'Inter',
				data: fontData,
				weight: 400,
				style: 'normal',
			},
		]
	});

	const resvg = new Resvg(svg);
	const pngData = resvg.render();
	const pngBuffer = pngData.asPng();

	return new Response(pngBuffer, {
		headers: {
			'Content-Type': 'image/png',
			'Cache-Control': 'public, max-age=31536000, immutable'
		}
	});
}